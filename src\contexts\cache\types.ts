/**
 * Cache Context Types
 * Type definitions for the unified cache context system
 */

// Re-export types from unified cache system
export type {
  BaseCacheEntry,
  CachedEmail,
  CachedEmailList,
  CachedThread,
  CachedCalendarEvent,
  CachedCalendarList,
  CachedAiConversation,
  CachedUserBehavior,
  CacheConfig,
  ModuleCacheConfig,
  CacheStats,
  CacheOperationResult,
  InvalidationOptions
} from '@/lib/cache/unified/types'

// Base cache types
export interface CacheKey {
  module: string
  key: string
  userId?: string
}

export type CacheNamespace = 'email' | 'calendar' | 'meet' | 'ai'

export interface CacheOperation {
  type: 'get' | 'set' | 'delete' | 'invalidate'
  key: string
  module: string
  userId?: string
  timestamp: number
}

export interface CacheQuery {
  module?: string
  userId?: string
  pattern?: string
  limit?: number
  offset?: number
}

export interface CacheInvalidation {
  module: string
  pattern?: string
  userId?: string
  force?: boolean
}

// Email cache data types
export interface EmailCacheData {
  emails: CachedEmail[]
  threads: CachedThread[]
  folders: string[]
  labels: string[]
}

// Calendar cache data types
export interface CalendarCacheData {
  events: CachedCalendarEvent[]
  calendars: CachedCalendarList[]
  settings: any
  colors: any
}

// Meet cache data types (removed as per user requirements)

// AI cache data types
export interface AICacheData {
  conversations: CachedAiConversation[]
  userBehavior: CachedUserBehavior[]
  knowledgeGraph: any[]
  insights: any[]
}

// Unified cache data structure
export interface UnifiedCacheData {
  email: EmailCacheData
  calendar: CalendarCacheData
  ai: AICacheData
}

// Cache context configuration
export interface CacheContextConfig {
  modules: {
    gmail: { enabled: boolean; ttl: number; maxSize: number }
    calendar: { enabled: boolean; ttl: number; maxSize: number }

    aiAgent: { enabled: boolean; ttl: number; maxSize: number }
  }
  autoPreload: boolean
  debugMode: boolean
}

// Cache provider props
export interface CacheProviderProps {
  children: React.ReactNode
  config?: Partial<CacheContextConfig>
  userId?: string
  autoPreload?: boolean
}

// Cache hook return types
export interface CacheHookResult<T> {
  data: T | null
  isLoading: boolean
  error: string | null
  refresh: () => Promise<void>
  invalidate: () => void
}

// Specific cache hook types
export interface EmailCacheHook {
  getEmails: (folder: string) => any[]
  setEmails: (folder: string, emails: any[]) => boolean
  invalidateEmails: (folder?: string) => number
  preloadEmails: () => Promise<void>
}

export interface CalendarCacheHook {
  getEvents: (timeRange?: string) => any[]
  setEvents: (events: any[], timeRange?: string) => boolean
  invalidateEvents: (pattern?: string) => number
  preloadEvents: () => Promise<void>
}

export interface MeetCacheHook {
  getSpaces: () => any[]
  setSpaces: (spaces: any[]) => boolean
  getConferences: () => any[]
  setConferences: (conferences: any[]) => boolean
  invalidateMeet: (pattern?: string) => number
  preloadMeet: () => Promise<void>
}

export interface AiCacheHook {
  getConversations: () => any[]
  setConversations: (conversations: any[]) => boolean
  getUserBehavior: () => any
  setUserBehavior: (behavior: any) => boolean
  invalidateAi: (pattern?: string) => number
  preloadAi: () => Promise<void>
}

// Cache monitoring types
export interface CacheMonitorData {
  stats: CacheStats
  namespaces: Record<CacheNamespace, number>
  recentOperations: CacheOperation[]
  performance: {
    hitRate: number
    missRate: number
    averageResponseTime: number
  }
}

// Cache event types
export type CacheEventType = 'hit' | 'miss' | 'set' | 'delete' | 'invalidate' | 'cleanup'

export interface CacheEvent {
  type: CacheEventType
  module: string
  key: string
  userId?: string
  timestamp: number
  metadata?: any
}

// Cache listener types
export type CacheEventListener = (event: CacheEvent) => void

export interface CacheEventEmitter {
  on: (eventType: CacheEventType, listener: CacheEventListener) => void
  off: (eventType: CacheEventType, listener: CacheEventListener) => void
  emit: (event: CacheEvent) => void
}
