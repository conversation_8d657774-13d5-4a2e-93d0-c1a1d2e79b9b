import { aiAgentService } from './aiAgent'
import { 
  getInboxEmails, 
  getSentEmails, 
  getEmailDetails,
  sendEmail,
  replyToEmail,
  createDraft,
  sendDraft,
  EmailMessage,
  InboxEmail,
  DraftData
} from '../gmail'
import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API || "AIzaSyAwHr4qa8SLtb7RMqaSRFzgAo6YwpP2Ga0")
const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || "gemini-2.5-flash-lite-preview-06-17" })

export interface EmailAnalysisRequest {
  query?: string
  dateFrom?: Date
  dateTo?: Date
  category?: 'all' | 'unread' | 'important' | 'starred'
  limit?: number
}

export interface EmailSummaryResult {
  totalEmails: number
  unreadCount: number
  importantEmails: InboxEmail[]
  summary: string
  actionItems: string[]
  categories: {
    [key: string]: {
      count: number
      emails: InboxEmail[]
    }
  }
  insights: string[]
}

export interface EmailComposeRequest {
  to: string | string[]
  cc?: string | string[]
  bcc?: string | string[]
  subject?: string
  context?: string
  tone?: 'professional' | 'casual' | 'friendly' | 'formal'
  purpose?: 'inquiry' | 'follow_up' | 'meeting_request' | 'update' | 'thank_you' | 'custom'
  keyPoints?: string[]
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}

export interface EmailReplyRequest {
  emailId: string
  context?: string
  tone?: 'professional' | 'casual' | 'friendly' | 'formal'
  replyType?: 'reply' | 'reply_all'
  keyPoints?: string[]
}

class AIAgentEmailService {
  
  async analyzeEmails(userId: string, request: EmailAnalysisRequest): Promise<EmailSummaryResult> {
    try {
      // Fetch emails based on request parameters
      const emailResult = await getInboxEmails(
        userId,
        request.limit || 50,
        undefined,
        undefined,
        undefined,
        request.dateFrom,
        request.dateTo
      )

      const emails = emailResult.emails

      // Filter emails based on category
      let filteredEmails = emails
      if (request.category === 'unread') {
        filteredEmails = emails.filter(email => !email.isRead)
      } else if (request.category === 'important') {
        filteredEmails = emails.filter(email => 
          email.labels?.some(label => label.name === 'IMPORTANT') || email.isStarred
        )
      } else if (request.category === 'starred') {
        filteredEmails = emails.filter(email => email.isStarred)
      }

      // Use AI to analyze and summarize emails
      const analysisPrompt = `
      Analyze the following ${filteredEmails.length} emails and provide a comprehensive summary:

      ${request.query ? `User's specific query: "${request.query}"` : 'Provide general analysis and insights.'}

      Emails to analyze:
      ${filteredEmails.map((email, index) => `
      Email ${index + 1}:
      From: ${email.from}
      Subject: ${email.subject}
      Date: ${email.date.toISOString()}
      Read: ${email.isRead ? 'Yes' : 'No'}
      Starred: ${email.isStarred ? 'Yes' : 'No'}
      Snippet: ${email.snippet}
      `).join('\n')}

      Please provide:
      1. A concise overall summary (2-3 sentences)
      2. List of action items that need attention
      3. Categorization of emails (work, personal, promotional, bills, etc.)
      4. Key insights and patterns
      5. Important/urgent emails that need immediate attention

      Return as JSON with this structure:
      {
        "summary": "Overall summary text",
        "actionItems": ["action 1", "action 2"],
        "categories": {
          "work": { "count": 0, "emailIds": [] },
          "personal": { "count": 0, "emailIds": [] }
        },
        "insights": ["insight 1", "insight 2"],
        "importantEmailIds": ["id1", "id2"]
      }
      `

      const result = await model.generateContent(analysisPrompt)
      const response = await result.response
      const text = response.text()

      // Parse AI response
      const jsonMatch = text.match(/\{[\s\S]*\}/)
      let analysisData: any = {}
      
      if (jsonMatch) {
        try {
          analysisData = JSON.parse(jsonMatch[0])
        } catch (error) {
          console.error('Error parsing AI analysis:', error)
        }
      }

      // Build categorized emails
      const categories: { [key: string]: { count: number; emails: InboxEmail[] } } = {}
      
      if (analysisData.categories) {
        for (const [category, data] of Object.entries(analysisData.categories as any)) {
          const categoryEmails = filteredEmails.filter(email => 
            (data as any).emailIds?.includes(email.id)
          )
          categories[category] = {
            count: (data as any).count || categoryEmails.length,
            emails: categoryEmails
          }
        }
      }

      // Get important emails
      const importantEmails = filteredEmails.filter(email => 
        analysisData.importantEmailIds?.includes(email.id) || 
        email.isStarred || 
        !email.isRead
      ).slice(0, 10) // Limit to top 10 important emails

      return {
        totalEmails: emails.length,
        unreadCount: emails.filter(e => !e.isRead).length,
        importantEmails,
        summary: analysisData.summary || 'Email analysis completed',
        actionItems: analysisData.actionItems || [],
        categories,
        insights: analysisData.insights || []
      }

    } catch (error) {
      console.error('Error analyzing emails:', error)
      throw error
    }
  }

  async composeEmail(userId: string, request: EmailComposeRequest): Promise<{ subject: string; htmlBody: string; textBody: string }> {
    try {
      const recipients = Array.isArray(request.to) ? request.to.join(', ') : request.to
      const ccRecipients = request.cc ? (Array.isArray(request.cc) ? request.cc.join(', ') : request.cc) : ''
      
      const composePrompt = `
      Compose a professional email with the following requirements:

      Recipients: ${recipients}
      ${ccRecipients ? `CC: ${ccRecipients}` : ''}
      ${request.subject ? `Subject: ${request.subject}` : 'Generate appropriate subject'}
      
      Context: ${request.context || 'General email'}
      Tone: ${request.tone || 'professional'}
      Purpose: ${request.purpose || 'general communication'}
      
      ${request.keyPoints && request.keyPoints.length > 0 ? `
      Key points to include:
      ${request.keyPoints.map((point, index) => `${index + 1}. ${point}`).join('\n')}
      ` : ''}

      Please generate:
      1. An appropriate subject line (if not provided)
      2. A well-structured HTML email body
      3. A plain text version of the email

      Guidelines:
      - Use proper email etiquette and formatting
      - Include appropriate greeting and closing
      - Make the content clear and actionable
      - Match the requested tone and purpose
      - Keep it concise but comprehensive

      Return as JSON:
      {
        "subject": "Email subject line",
        "htmlBody": "HTML formatted email body",
        "textBody": "Plain text version"
      }
      `

      const result = await model.generateContent(composePrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const emailData = JSON.parse(jsonMatch[0])
        return {
          subject: request.subject || emailData.subject,
          htmlBody: emailData.htmlBody,
          textBody: emailData.textBody
        }
      }

      throw new Error('Failed to generate email content')

    } catch (error) {
      console.error('Error composing email:', error)
      throw error
    }
  }

  async sendComposedEmail(userId: string, emailData: EmailMessage): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const result = await sendEmail(userId, emailData)
      return result
    } catch (error) {
      console.error('Error sending composed email:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send email'
      }
    }
  }

  async generateReply(userId: string, request: EmailReplyRequest): Promise<{ subject: string; htmlBody: string; textBody: string }> {
    try {
      // Get the original email details
      const emailDetails = await getEmailDetails(userId, request.emailId)
      
      const replyPrompt = `
      Generate a reply to the following email:

      Original Email:
      From: ${emailDetails.from}
      Subject: ${emailDetails.subject}
      Date: ${emailDetails.date}
      Body: ${emailDetails.body || emailDetails.snippet}

      Reply Requirements:
      Type: ${request.replyType || 'reply'}
      Tone: ${request.tone || 'professional'}
      Context: ${request.context || 'Standard reply'}
      
      ${request.keyPoints && request.keyPoints.length > 0 ? `
      Key points to address:
      ${request.keyPoints.map((point, index) => `${index + 1}. ${point}`).join('\n')}
      ` : ''}

      Please generate:
      1. Appropriate reply subject (with Re: prefix if needed)
      2. HTML formatted reply body
      3. Plain text version

      Guidelines:
      - Reference the original email appropriately
      - Use proper reply formatting and etiquette
      - Address the sender's points or questions
      - Match the requested tone
      - Include appropriate greeting and closing

      Return as JSON:
      {
        "subject": "Reply subject line",
        "htmlBody": "HTML formatted reply body",
        "textBody": "Plain text version"
      }
      `

      const result = await model.generateContent(replyPrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const replyData = JSON.parse(jsonMatch[0])
        return replyData
      }

      throw new Error('Failed to generate reply content')

    } catch (error) {
      console.error('Error generating reply:', error)
      throw error
    }
  }

  async sendReply(userId: string, emailId: string, replyData: any): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Get original email details for thread information
      const originalEmail = await getEmailDetails(userId, emailId)
      
      const result = await replyToEmail(userId, {
        to: originalEmail.from,
        subject: replyData.subject,
        htmlBody: replyData.htmlBody,
        textBody: replyData.textBody,
        threadId: originalEmail.threadId,
        replyToMessageId: originalEmail.id
      })

      return result
    } catch (error) {
      console.error('Error sending reply:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send reply'
      }
    }
  }

  async createEmailDraft(userId: string, draftData: DraftData): Promise<{ success: boolean; draftId?: string; error?: string }> {
    try {
      const result = await createDraft(userId, draftData)
      return result
    } catch (error) {
      console.error('Error creating draft:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create draft'
      }
    }
  }

  async getEmailInsights(userId: string, days: number = 7): Promise<{
    totalEmails: number
    sentEmails: number
    unreadEmails: number
    topSenders: Array<{ email: string; count: number }>
    emailTrends: Array<{ date: string; received: number; sent: number }>
    categories: { [key: string]: number }
  }> {
    try {
      const dateFrom = new Date()
      dateFrom.setDate(dateFrom.getDate() - days)

      // Get inbox and sent emails
      const [inboxResult, sentResult] = await Promise.all([
        getInboxEmails(userId, 100, undefined, undefined, undefined, dateFrom),
        getSentEmails(userId, 100, undefined, undefined, dateFrom)
      ])

      const inboxEmails = inboxResult.emails
      const sentEmails = sentResult.emails

      // Calculate insights
      const totalEmails = inboxEmails.length
      const unreadEmails = inboxEmails.filter(e => !e.isRead).length
      
      // Top senders
      const senderCounts: { [key: string]: number } = {}
      inboxEmails.forEach(email => {
        senderCounts[email.from] = (senderCounts[email.from] || 0) + 1
      })
      
      const topSenders = Object.entries(senderCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([email, count]) => ({ email, count }))

      // Email trends by day
      const emailTrends: Array<{ date: string; received: number; sent: number }> = []
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]
        
        const receivedCount = inboxEmails.filter(email => 
          email.date.toISOString().split('T')[0] === dateStr
        ).length
        
        const sentCount = sentEmails.filter(email => 
          email.date.toISOString().split('T')[0] === dateStr
        ).length
        
        emailTrends.push({
          date: dateStr,
          received: receivedCount,
          sent: sentCount
        })
      }

      return {
        totalEmails,
        sentEmails: sentEmails.length,
        unreadEmails,
        topSenders,
        emailTrends,
        categories: {} // Could be enhanced with AI categorization
      }

    } catch (error) {
      console.error('Error getting email insights:', error)
      throw error
    }
  }
}

export const aiAgentEmailService = new AIAgentEmailService()
