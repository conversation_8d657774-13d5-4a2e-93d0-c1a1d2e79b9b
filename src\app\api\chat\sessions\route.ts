import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { unifiedEncryption as chatEncryption, ChatSession } from '@/lib/encryption/unifiedEncryption'

export async function GET(req: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get chat sessions for the user
    const chatSessions = await prisma.chatSession.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    // Decrypt sessions
    const decryptedSessions = chatEncryption.decryptChatSessions(chatSessions as any)

    return NextResponse.json({ sessions: decryptedSessions })
  } catch (error) {
    console.error('Error fetching chat sessions:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(req: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { title } = await req.json()

    // Create new chat session
    const newSession: ChatSession = {
      id: `session-${Date.now()}`,
      userId: user.id,
      title: title || 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isArchived: false
    }

    // Encrypt and save
    const encryptedSession = await chatEncryption.encryptChatSession(newSession)

    const savedSession = await prisma.chatSession.create({
      data: encryptedSession as any
    })

    return NextResponse.json({ session: newSession })
  } catch (error) {
    console.error('Error creating chat session:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { sessionId, title, messages, isArchived } = await req.json()

    // Get existing session
    const existingSession = await prisma.chatSession.findUnique({
      where: { id: sessionId, userId: user.id }
    })

    if (!existingSession) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 })
    }

    // Get existing session and decrypt it first
    const decryptedExistingSession = await chatEncryption.decryptChatSession(existingSession as any)

    // Update session
    const updatedSession: ChatSession = {
      id: sessionId,
      userId: user.id,
      title: title || decryptedExistingSession.title,
      messages: messages || decryptedExistingSession.messages,
      createdAt: decryptedExistingSession.createdAt,
      updatedAt: new Date(),
      isArchived: isArchived !== undefined ? isArchived : decryptedExistingSession.isArchived
    }

    // Encrypt and save
    const encryptedSession = await chatEncryption.encryptChatSession(updatedSession)

    await prisma.chatSession.update({
      where: { id: sessionId },
      data: {
        title: encryptedSession.title,
        messages: encryptedSession.messages,
        updatedAt: encryptedSession.updatedAt
      }
    })

    return NextResponse.json({ session: updatedSession })
  } catch (error) {
    console.error('Error updating chat session:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { searchParams } = new URL(req.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID required' }, { status: 400 })
    }

    // Delete session
    await prisma.chatSession.delete({
      where: {
        id: sessionId,
        userId: user.id
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting chat session:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
} 